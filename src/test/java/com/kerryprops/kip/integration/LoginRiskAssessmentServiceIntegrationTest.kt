package com.kerryprops.kip.integration

import com.kerryprops.kip.riskcontrol.general.RiskResult
import com.kerryprops.kip.riskcontrol.login.AssessmentDetail
import com.kerryprops.kip.riskcontrol.login.LoginRiskAssessRequestDto
import com.kerryprops.kip.riskcontrol.login.LoginRiskAssessmentService
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * Comprehensive API integration tests for login risk assessment endpoint.
 * Tests the complete HTTP API behavior against Drools rules with actual service integration.
 *
 *
 * Uses @SpringBootTest with RANDOM_PORT and RestAssured for full integration testing.
 * Enables Drools rule engine to test actual business rule evaluation.
 */
@DisplayName("Login Risk Assessment API Integration Tests")
internal class LoginRiskAssessmentServiceIntegrationTest : BaseIntegrationTest() {
    @Autowired
    private lateinit var loginRiskAssessmentService: LoginRiskAssessmentService

    @Test
    @DisplayName("虚拟手机号码登录全部商场都返回拒绝登录")
    fun shouldRejectVirtualPhoneNumberLoginAtAllMall() {
        // Define all legal mall codes
        val legalMallCodes = listOf("JAKC", "HKC", "KP")

        // Define additional mall codes to test comprehensive rejection logic
        // Including invalid/unknown mall codes to ensure rejection works regardless
        val additionalMallCodes = listOf("UNKNOWN", "INVALID", "TEST", "", null)

        // Combine all mall codes for comprehensive testing
        val allMallCodes = legalMallCodes + additionalMallCodes

        val virtualPhoneNumber = "***********"

        // Test each mall code (both legal and invalid) to ensure virtual phone numbers are always rejected
        allMallCodes.forEach { mallCode ->
            LoginRiskAssessRequestDto().apply {
                this.mallCode = mallCode
                phoneNumber = virtualPhoneNumber
                smsLoginPhoneNumberCountLast30Days = 1
                isFrozenMember = false
            }.also { request ->
                val response = loginRiskAssessmentService.assessRisk(request)

                // Assert that the assessment result is REJECT for this mall code
                // Virtual phone numbers should be rejected regardless of mall code
                assertThat(response).isNotNull()
                assertThat(response.riskResult)
                    .withFailMessage("Virtual phone number login should be REJECTED for ANY mall code, including: $mallCode")
                    .isEqualTo(RiskResult.REJECT)
                assertThat(response.mallCode).isEqualTo(mallCode)
                assertThat(response.assessmentTime).isNotNull
                assertThat(response.assessmentDetails).isNotNull.hasSize(1)
                assertThat(response.assessmentDetails).asList().containsExactly(
                    AssessmentDetail(
                        "40001",
                        "登录：虚拟号段被拦截"
                    )
                )
            }
        }

        // Additional test with different virtual phone number patterns to ensure comprehensive coverage
        // Generated from all virtual prefixes: China Mobile, China Unicom, China Telecom, China Broadcast
        val additionalVirtualNumbers = listOf(
            // China Mobile virtual numbers
            "16512345678", // 165 prefix
            "17031234567", // 1703 prefix
            "17051234567", // 1705 prefix
            "17061234567", // 1706 prefix

            // China Unicom virtual numbers
            "16712345678", // 167 prefix
            "17041234567", // 1704 prefix
            "17071234567", // 1707 prefix
            "17081234567", // 1708 prefix
            "17091234567", // 1709 prefix
            "17112345678", // 171 prefix

            // China Telecom virtual numbers
            "16212345678", // 162 prefix
            "17001234567", // 1700 prefix
            "17011234567", // 1701 prefix
            "17021234567", // 1702 prefix

            // China Broadcast virtual numbers
            "19212345678"  // 192 prefix
        )

        additionalVirtualNumbers.forEach { virtualNumber ->
            // Test with a legal mall code to ensure different virtual numbers are rejected
            LoginRiskAssessRequestDto().apply {
                this.mallCode = "JAKC" // Use a known legal mall code
                phoneNumber = virtualNumber
                smsLoginPhoneNumberCountLast30Days = 1
                isFrozenMember = false
            }.also { request ->
                val response = loginRiskAssessmentService.assessRisk(request)

                assertThat(response).isNotNull()
                assertThat(response.riskResult)
                    .withFailMessage("Virtual phone number $virtualNumber should be REJECTED regardless of mall code")
                    .isEqualTo(RiskResult.REJECT)
                assertThat(response.assessmentDetails).isNotNull.hasSize(1)
                assertThat(response.assessmentDetails).asList().containsExactly(
                    AssessmentDetail(
                        "40001",
                        "登录：虚拟号段被拦截"
                    )
                )
            }
        }
    }

    @Test
    @DisplayName("非虚拟手机号码登录全部商场都返回通过登录")
    fun shouldPassNonVirtualPhoneNumberLoginAtAllMall() {
        // Test with a legal mall code to ensure different virtual numbers are rejected
        LoginRiskAssessRequestDto().apply {
            this.mallCode = "JAKC" // Use a known legal mall code
            phoneNumber = "13831234567"
            smsLoginPhoneNumberCountLast30Days = 1
            isFrozenMember = false
        }.also { request ->
            val response = loginRiskAssessmentService.assessRisk(request)

            assertThat(response).isNotNull()
            assertThat(response.riskResult).isEqualTo(RiskResult.PASS)
            assertThat(response.assessmentDetails).asList().isEmpty()
            assertThat(response.assessmentTime).isNotNull
            assertThat(response.mallCode).isEqualTo("JAKC")
        }
    }

    @Test
    @DisplayName("smsLoginPhoneNumberCountLast30Days大于等于3时拒绝登录")
    fun shouldRejectLoginWhenSmsLoginPhoneNumberCountLast30DaysIsGreaterOrEqualThan3() {
        listOf(3, 4).forEach { i ->
            LoginRiskAssessRequestDto().apply {
                this.mallCode = "JAKC" // Use a known legal mall code
                phoneNumber = "13831234567"
                smsLoginPhoneNumberCountLast30Days = i
                isFrozenMember = false
            }.also { request ->
                val response = loginRiskAssessmentService.assessRisk(request)

                assertThat(response).isNotNull()
                assertThat(response.riskResult)
                    .isEqualTo(RiskResult.REJECT)
                assertThat(response.assessmentDetails).isNotNull.hasSize(1)
                assertThat(response.assessmentDetails).asList().containsExactly(
                    AssessmentDetail(
                        "40002",
                        "登录：ID校验超限被拦截"
                    )
                )
            }
        }

    }

    @Test
    @DisplayName("smsLoginPhoneNumberCountLast30Days小于3时允许登录")
    fun shouldPassLoginWhenSmsLoginPhoneNumberCountLast30DaysIsLessThan3() {
        listOf(1, 2).forEach { i ->
            LoginRiskAssessRequestDto().apply {
                this.mallCode = "JAKC" // Use a known legal mall code
                phoneNumber = "13831234567"
                smsLoginPhoneNumberCountLast30Days = i
                isFrozenMember = false
            }.also { request ->
                val response = loginRiskAssessmentService.assessRisk(request)

                assertThat(response).isNotNull()
                assertThat(response.riskResult).isEqualTo(RiskResult.PASS)
                assertThat(response.assessmentDetails).asList().isEmpty()
                assertThat(response.assessmentTime).isNotNull
                assertThat(response.mallCode).isEqualTo("JAKC")
            }
        }

    }

    @Test
    @DisplayName("smsLoginPhoneNumberCountLast30Days大于等于3并且是虚拟号码 拒绝登录，并返回所有命中规则")
    fun shouldRejectLoginWhenSmsLoginPhoneNumberCountLast30DaysIsGreaterOrEqualThan3AndIsVirtualPhoneNumber() {
        LoginRiskAssessRequestDto().apply {
            this.mallCode = "JAKC" // Use a known legal mall code
            phoneNumber = "16212345678"
            smsLoginPhoneNumberCountLast30Days = 4
            isFrozenMember = false
        }.also { request ->
            val response = loginRiskAssessmentService.assessRisk(request)

            assertThat(response).isNotNull()
            assertThat(response.riskResult)
                .isEqualTo(RiskResult.REJECT)
            assertThat(response.assessmentDetails).isNotNull.hasSize(2)
            assertThat(response.assessmentDetails).asList().containsExactly(
                AssessmentDetail(
                    "40001",
                    "登录：虚拟号段被拦截"
                ),
                AssessmentDetail(
                    "40002",
                    "登录：ID校验超限被拦截"
                )
            )
        }
    }

}
