package com.kerryprops.kip.riskcontrol.login;

import com.kerryprops.kip.riskcontrol.general.PhoneNumberUtil;
import com.kerryprops.kip.riskcontrol.general.RiskResult;
import com.kerryprops.kip.riskcontrol.shared.DroolsRuleEngineService;
import com.kerryprops.kip.riskcontrol.shared.RiskAssessmentContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;

/**
 * Service for login risk assessment operations.
 * Follows functional programming principles and TDD methodology.
 * 
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoginRiskAssessmentService {

    private final DroolsRuleEngineService droolsRuleEngineService;
    private final PhoneNumberUtil phoneNumberUtil;

    /**
     * Assesses risk for login scenario using Drools rules engine.
     * This method follows functional programming principles by being stateless
     * and immutable in its approach.
     *
     * @param request the login risk assessment request
     * @return the risk assessment result
     */
    public LoginRiskAssessResultDto assessRisk(LoginRiskAssessRequestDto request) {
        log.info("Starting login risk assessment for mallCode: {}, phoneNumber: {}", 
                request.getMallCode(), request.getPhoneNumber());

        // Create risk assessment context
        RiskAssessmentContext context = createRiskAssessmentContext(request);
        
        // Evaluate risk using Drools rules
        RiskAssessmentContext evaluatedContext = droolsRuleEngineService.evaluateRisk(context);
        
        // Convert to response DTO
        LoginRiskAssessResultDto result = mapToResultDto(evaluatedContext, request);
        
        log.info("Login risk assessment completed - mallCode: {}, phoneNumber: {}, result: {}", 
                request.getMallCode(), request.getPhoneNumber(), result.riskResult());
        
        return result;
    }

    /**
     * Creates a RiskAssessmentContext from the request DTO.
     * Follows functional programming principles with immutable object creation.
     *
     * @param request the login risk assessment request
     * @return the populated risk assessment context
     */
    private RiskAssessmentContext createRiskAssessmentContext(LoginRiskAssessRequestDto request) {
        // Map mall code string to enum
        RiskAssessmentContext.MallCode mallCode = mapMallCode(request.getMallCode());
        
        // Create context with business scenario and mall code
        RiskAssessmentContext context = RiskAssessmentContext.create(
                RiskAssessmentContext.BusinessScenario.LOGIN, 
                mallCode
        );
        
        // Set phone number information
        if (request.getPhoneNumber() != null) {
            boolean isVirtual = phoneNumberUtil.isVirtualNumber(request.getPhoneNumber());
            boolean isValid = phoneNumberUtil.isValidPhoneNumber(request.getPhoneNumber());
            
            context.withPhoneInfo(
                    request.getPhoneNumber(),
                    isValid, // Assuming valid numbers are mainland numbers
                    isVirtual
            );
        }
        
        // Set member information
        context.withMemberInfo(null, request.getIsFrozenMember());
        
        // Set SMS login count
        context.setSmsLoginPhoneNumberCountLast30Days(request.getSmsLoginPhoneNumberCountLast30Days());
        
        // Set Tencent risk response if available
        context.setTxRiskResponse(request.getTxRiskResponse());
        
        return context;
    }

    /**
     * Maps mall code string to MallCode enum.
     * Follows functional programming principles with pure function approach.
     *
     * @param mallCodeString the mall code string
     * @return the corresponding MallCode enum
     */
    private RiskAssessmentContext.MallCode mapMallCode(String mallCodeString) {
        if (mallCodeString == null) {
            return RiskAssessmentContext.MallCode.UNKNOWN;
        }
        
        return switch (mallCodeString.toUpperCase()) {
            case "JAKC" -> RiskAssessmentContext.MallCode.JAKC;
            case "HKC" -> RiskAssessmentContext.MallCode.HKC;
            case "KP" -> RiskAssessmentContext.MallCode.KP;
            default -> RiskAssessmentContext.MallCode.UNKNOWN;
        };
    }

    /**
     * Maps the evaluated context to the result DTO.
     * Follows functional programming principles with immutable object creation.
     *
     * @param context the evaluated risk assessment context
     * @param request the original request for reference
     * @return the login risk assessment result DTO
     */
    private LoginRiskAssessResultDto mapToResultDto(RiskAssessmentContext context, LoginRiskAssessRequestDto request) {
        // Create assessment details - only for REJECT results, empty list for PASS results
        var assessmentDetails = context.getRiskResult() == RiskResult.REJECT && context.getAssessmentDetails() != null
                ? Collections.singletonList(new AssessmentDetail("40001", context.getAssessmentDetails()))
                : Collections.<AssessmentDetail>emptyList();

        return new LoginRiskAssessResultDto(
                request.getMallCode(),
                null, // unionId not available in current request
                context.getRiskResult(),
                assessmentDetails,
                LocalDateTime.now()
        );
    }
}
