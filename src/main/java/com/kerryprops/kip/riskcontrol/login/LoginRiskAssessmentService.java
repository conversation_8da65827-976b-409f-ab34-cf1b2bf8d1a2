package com.kerryprops.kip.riskcontrol.login;

import com.kerryprops.kip.riskcontrol.general.PhoneNumberUtil;
import com.kerryprops.kip.riskcontrol.general.RiskResult;
import com.kerryprops.kip.riskcontrol.shared.DroolsRuleEngineService;
import com.kerryprops.kip.riskcontrol.shared.RiskAssessmentContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Service for login risk assessment operations.
 * Follows functional programming principles and TDD methodology.
 * 
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoginRiskAssessmentService {

    private final DroolsRuleEngineService droolsRuleEngineService;
    private final PhoneNumberUtil phoneNumberUtil;

    /**
     * Assesses risk for login scenario using Drools rules engine.
     * This method follows functional programming principles by being stateless
     * and immutable in its approach.
     *
     * @param request the login risk assessment request
     * @return the risk assessment result
     */
    public LoginRiskAssessResultDto assessRisk(LoginRiskAssessRequestDto request) {
        log.info("Starting login risk assessment for mallCode: {}, phoneNumber: {}", 
                request.getMallCode(), request.getPhoneNumber());

        // Create risk assessment context
        RiskAssessmentContext context = createRiskAssessmentContext(request);
        
        // Evaluate risk using Drools rules
        RiskAssessmentContext evaluatedContext = droolsRuleEngineService.evaluateRisk(context);
        
        // Convert to response DTO
        LoginRiskAssessResultDto result = mapToResultDto(evaluatedContext, request);
        
        log.info("Login risk assessment completed - mallCode: {}, phoneNumber: {}, result: {}", 
                request.getMallCode(), request.getPhoneNumber(), result.riskResult());
        
        return result;
    }

    /**
     * Creates a RiskAssessmentContext from the request DTO.
     * Follows functional programming principles with immutable object creation.
     *
     * @param request the login risk assessment request
     * @return the populated risk assessment context
     */
    private RiskAssessmentContext createRiskAssessmentContext(LoginRiskAssessRequestDto request) {
        // Map mall code string to enum
        RiskAssessmentContext.MallCode mallCode = mapMallCode(request.getMallCode());
        
        // Create context with business scenario and mall code
        RiskAssessmentContext context = RiskAssessmentContext.create(
                RiskAssessmentContext.BusinessScenario.LOGIN, 
                mallCode
        );
        
        // Set phone number information
        if (request.getPhoneNumber() != null) {
            boolean isVirtual = phoneNumberUtil.isVirtualNumber(request.getPhoneNumber());
            boolean isValid = phoneNumberUtil.isValidPhoneNumber(request.getPhoneNumber());
            
            context.withPhoneInfo(
                    request.getPhoneNumber(),
                    isValid, // Assuming valid numbers are mainland numbers
                    isVirtual
            );
        }
        
        // Set member information
        context.withMemberInfo(null, request.getIsFrozenMember());
        
        // Set SMS login count
        context.setSmsLoginPhoneNumberCountLast30Days(request.getSmsLoginPhoneNumberCountLast30Days());
        
        // Set Tencent risk response if available
        context.setTxRiskResponse(request.getTxRiskResponse());
        
        return context;
    }

    /**
     * Maps mall code string to MallCode enum.
     * Follows functional programming principles with pure function approach.
     *
     * @param mallCodeString the mall code string
     * @return the corresponding MallCode enum
     */
    private RiskAssessmentContext.MallCode mapMallCode(String mallCodeString) {
        if (mallCodeString == null) {
            return RiskAssessmentContext.MallCode.UNKNOWN;
        }
        
        return switch (mallCodeString.toUpperCase()) {
            case "JAKC" -> RiskAssessmentContext.MallCode.JAKC;
            case "HKC" -> RiskAssessmentContext.MallCode.HKC;
            case "KP" -> RiskAssessmentContext.MallCode.KP;
            default -> RiskAssessmentContext.MallCode.UNKNOWN;
        };
    }

    /**
     * Maps the evaluated context to the result DTO.
     * Follows functional programming principles with immutable object creation.
     *
     * @param context the evaluated risk assessment context
     * @param request the original request for reference
     * @return the login risk assessment result DTO
     */
    private LoginRiskAssessResultDto mapToResultDto(RiskAssessmentContext context, LoginRiskAssessRequestDto request) {
        // Create assessment details - handle multiple triggered rules
        var assessmentDetails = createAssessmentDetails(context, request);

        return new LoginRiskAssessResultDto(
                request.getMallCode(),
                null, // unionId not available in current request
                context.getRiskResult(),
                assessmentDetails,
                LocalDateTime.now()
        );
    }

    /**
     * Creates assessment details list from triggered rules.
     * Handles multiple rules being triggered simultaneously.
     *
     * @param context the evaluated risk assessment context
     * @param request the original request for reference
     * @return list of assessment details
     */
    private List<AssessmentDetail> createAssessmentDetails(RiskAssessmentContext context, LoginRiskAssessRequestDto request) {
        if (context.getRiskResult() != RiskResult.REJECT) {
            return Collections.emptyList();
        }

        List<AssessmentDetail> details = new ArrayList<>();

        // Check if we have multiple assessment details
        if (context.getAssessmentDetailsList() != null && !context.getAssessmentDetailsList().isEmpty()) {
            for (String detail : context.getAssessmentDetailsList()) {
                String errorCode = determineErrorCodeForDetail(detail, request);
                details.add(new AssessmentDetail(errorCode, detail));
            }
        } else if (context.getAssessmentDetails() != null) {
            // Fallback to single assessment detail for backward compatibility
            String errorCode = determineErrorCodeForDetail(context.getAssessmentDetails(), request);
            details.add(new AssessmentDetail(errorCode, context.getAssessmentDetails()));
        }

        return details;
    }

    /**
     * Determines the appropriate error code based on the assessment detail content.
     * Follows functional programming principles with pure function logic.
     *
     * @param detail the assessment detail description
     * @param request the original request for reference
     * @return the appropriate error code
     */
    private String determineErrorCodeForDetail(String detail, LoginRiskAssessRequestDto request) {
        // Check if detail is related to SMS login phone number count
        if (detail.contains("ID校验超限") && request.getSmsLoginPhoneNumberCountLast30Days() != null
                && request.getSmsLoginPhoneNumberCountLast30Days() >= 3) {
            return "40002";
        }

        // Default error code for other rejections (including virtual phone number)
        return "40001";
    }
}
