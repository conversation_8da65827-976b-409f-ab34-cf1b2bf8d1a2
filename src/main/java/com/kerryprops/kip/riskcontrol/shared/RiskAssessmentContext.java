package com.kerryprops.kip.riskcontrol.shared;

import com.kerryprops.kip.riskcontrol.general.RiskResult;
import com.kerryprops.kip.riskcontrol.general.TxRiskResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 综合风控评估上下文模型.
 * 包含所有风控评估所需的信息，支持多种业务场景的风控规则.
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiskAssessmentContext {

    /**
     * 业务场景.
     */
    private BusinessScenario businessScenario;
    /**
     * 商场代码.
     */
    private MallCode mallCode;
    private Integer smsLoginPhoneNumberCountLast30Days;

    private TxRiskResponse txRiskResponse;
    // ========== 基础信息 ==========

    /**
     * IP地址.
     */
    private String ipAddress;

    /**
     * 手机号.
     */
    private String phoneNumber;
    /**
     * 是否为大陆手机号.
     */
    private Boolean isMainlandPhoneNumber;

    // ========== 手机号相关信息 ==========
    /**
     * 是否为虚拟号段.
     */
    private Boolean isVirtualPhoneNumber;
    /**
     * 会员积分.
     */
    private Integer memberPoints;
    /**
     * 是否为冻结会员.
     */
    private Boolean isFrozenMember;

    // ========== 会员相关信息 ==========
    /**
     * 腾讯风控评估结果.
     */
    private String tencentRiskLevel; // "高", "中", "低"
    /**
     * ID校验结果 - 一个月内同一UnionID登录次数.
     */
    private Integer unionIdLoginCount;

    // ========== 风控相关信息 ==========
    /**
     * ID校验结果 - 一个月内同一手机号登录次数.
     */
    private Integer phoneNumberLoginCount;
    /**
     * 风控评估结果.
     */
    private RiskResult riskResult;
    /**
     * 拦截提示语.
     */
    private String blockMessage;

    // ========== 评估结果 ==========
    /**
     * 评估详情.
     */
    private String assessmentDetails;

    /**
     * 多个评估详情列表 - 用于支持多个规则同时触发的场景.
     */
    private List<String> assessmentDetailsList;

    /**
     * 创建风控评估上下文.
     *
     * @param businessScenario 业务场景
     * @param mallCode         商场代码
     * @return RiskAssessmentContext
     */
    public static RiskAssessmentContext create(BusinessScenario businessScenario, MallCode mallCode) {
        RiskAssessmentContext context = new RiskAssessmentContext();
        context.setBusinessScenario(businessScenario);
        context.setMallCode(mallCode);
        return context;
    }

    /**
     * 设置手机号相关信息.
     *
     * @param phoneNumber           手机号
     * @param isMainlandPhoneNumber 是否为大陆手机号
     * @param isVirtualPhoneNumber  是否为虚拟号段
     * @return this
     */
    public RiskAssessmentContext withPhoneInfo(String phoneNumber, Boolean isMainlandPhoneNumber, 
            Boolean isVirtualPhoneNumber) {
        this.phoneNumber = phoneNumber;
        this.isMainlandPhoneNumber = isMainlandPhoneNumber;
        this.isVirtualPhoneNumber = isVirtualPhoneNumber;
        return this;
    }

    /**
     * 设置会员相关信息.
     *
     * @param memberPoints   会员积分
     * @param isFrozenMember 是否为冻结会员
     * @return this
     */
    public RiskAssessmentContext withMemberInfo(Integer memberPoints, Boolean isFrozenMember) {
        this.memberPoints = memberPoints;
        this.isFrozenMember = isFrozenMember;
        return this;
    }

    /**
     * 添加评估详情到列表中.
     * 用于支持多个规则同时触发的场景.
     *
     * @param detail 评估详情
     */
    public void addAssessmentDetail(String detail) {
        if (this.assessmentDetailsList == null) {
            this.assessmentDetailsList = new ArrayList<>();
        }
        this.assessmentDetailsList.add(detail);
    }
    /**
     * 业务场景类型.
     */
    @Getter
    public enum BusinessScenario {
        /** 登录. */
        LOGIN("登录"),
        /** 会员停车权益. */
        PARKING_BENEFIT("会员停车权益"),
        /** 拍照积分. */
        PHOTO_POINTS("拍照积分"),
        /** 营销场景. */
        MARKETING("营销场景"),
        /** 销售积分. */
        SALES_POINTS("销售积分"),
        /** 核销券. */
        COUPON_VERIFICATION("核销券");

        /** 描述. */
        private final String description;

        BusinessScenario(String description) {
            this.description = description;
        }

    }

    /**
     * 商场代码枚举.
     */
    @Getter
    public enum MallCode {
        /** JAKC商场. */
        JAKC("JAKC"),
        /** HKC商场. */
        HKC("HKC"),
        /** KP商场. */
        KP("KP"),
        UNKNOWN("Unknown");

        /** 商场代码. */
        private final String code;

        MallCode(String code) {
            this.code = code;
        }

    }
}
