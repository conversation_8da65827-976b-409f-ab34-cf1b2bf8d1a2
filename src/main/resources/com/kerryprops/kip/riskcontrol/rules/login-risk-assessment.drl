package com.kerryprops.kip.riskcontrol.rules;

import com.kerryprops.kip.riskcontrol.shared.RiskAssessmentContext;
import com.kerryprops.kip.riskcontrol.general.RiskResult;

/**
 * Risk assessment rules specific to LOGIN business scenario.
 *
 * Business Rules:
 * - 非大陆IP -> Y (不拦截)
 * - 虚拟号段 -> N (拦截) - 适用于所有商场
 * - ID校验超限 -> N (拦截) - 适用于所有商场
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */


// ========== 手机号段规则 ==========

// 虚拟号段：登录 -> N (拦截) - 适用于所有商场
rule "Login - Virtual Phone Number - REJECT"
    salience 90
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.LOGIN,
            isVirtualPhoneNumber == true
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("无提示，收不到验证码");
        $context.addAssessmentDetail("登录：虚拟号段被拦截");
        System.out.println("Rule executed: Login - Virtual Phone Number - REJECT");
end

// ========== ID校验规则 ==========

// ID校验：超限 -> N (拦截) - 适用于所有商场
rule "Login - ID Validation Exceeded - REJECT"
    salience 70
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.LOGIN,
            unionIdLoginCount != null,
            unionIdLoginCount > 5
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("此微信登录会员账户已超过限额，您可登录近期使用过的会员账户");
        $context.addAssessmentDetail("登录：ID校验超限被拦截");
        System.out.println("Rule executed: Login - ID Validation Exceeded - REJECT");
end

// SMS登录手机号次数：超限 -> N (拦截) - 适用于所有商场
rule "Login - SMS Phone Number Count Exceeded - REJECT"
    salience 80
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.LOGIN,
            smsLoginPhoneNumberCountLast30Days != null,
            smsLoginPhoneNumberCountLast30Days >= 3
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("此手机号登录次数已超过限额，请稍后重试");
        $context.addAssessmentDetail("登录：ID校验超限被拦截");
        System.out.println("Rule executed: Login - SMS Phone Number Count Exceeded - REJECT");
end

// ========== 登录场景默认通过规则 ==========

// 登录场景：默认通过 (当没有其他拦截规则触发时)
rule "Login - Default PASS"
    salience -50  // 最低优先级，只有当没有其他规则触发时才执行
    no-loop true
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.LOGIN,
            riskResult == null,
            isVirtualPhoneNumber != true,
            (unionIdLoginCount == null || unionIdLoginCount <= 5),
            (smsLoginPhoneNumberCountLast30Days == null || smsLoginPhoneNumberCountLast30Days < 3)
        )
    then
        $context.setRiskResult(RiskResult.PASS);
        $context.addAssessmentDetail("登录场景：默认通过");
        System.out.println("Rule executed: Login - Default PASS");
end
